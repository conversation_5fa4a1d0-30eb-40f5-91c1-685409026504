@tool
def process_insurance(insurance = [], userLabel = {}, corpInfo = {}, screenshotInfo={}, source=None, hitPolicies=[]):
    # 处理用户保险标志状态
    user_insurance_flg = ""
    located_policy_type = ""
    if not insurance:
        user_insurance_flg = "none"
        located_policy_type = "none"
    else:
        all_free = all(policy.get('isFree', False) for policy in insurance)
        user_insurance_flg = "gift" if all_free else "normal"

    # 新增参数处理
    located_policy_flg = len(hitPolicies) > 0
    located_policy_size = len(hitPolicies)
    
    if not hitPolicies:
        located_policy_type = "none"
    else:
        hit_policies_data = [policy for policy in insurance if policy.get('policyNo') in hitPolicies]
        all_hit_free = all(policy.get('isFree', False) for policy in hit_policies_data)
        located_policy_type = "gift" if all_hit_free else "normal"

    # 统计非赠险保单数量
    user_insurance_size = sum(1 for policy in insurance if not policy.get('isFree', False))

    # 统计赠险保单数量
    user_free_insurance_size = sum(1 for policy in insurance if policy.get('isFree', True))

    # 处理降级标记状态
    user_allow_reduce_flg = False
    for policy in insurance:
        if policy.get('policyNo') in hitPolicies:
            user_allow_reduce_flg = policy.get('downGradeFlag', 'N') == 'Y'
            break  # 找到即终止

    # hitPolicies中的保单退保申请状态获取
    # 统计"已退保"或"退保申请中"的数量
    FAIL_STATUSES = {"已退保", "申请中"}
    count = sum(
        item.get("cancelApplyStatus", "") in FAIL_STATUSES
        for item in insurance
        if item.get("policyNo", "") in hitPolicies
    )
    cancel_apply_status = count == len(hitPolicies)

    tag_count = userLabel.get('tagCount',{})
    phone_from_user = userLabel.get('phoneFromUser', '')
    user_allow_retention_flg = True
    if isinstance(tag_count, dict):
        surrender_demand = tag_count.get('退保', 0)
        if surrender_demand > 7:
            user_allow_retention_flg = False
    return {
        "user_insurance_flg": user_insurance_flg,
        "user_allow_retention_flg": user_allow_retention_flg,
        "user_allow_reduce_flg": user_allow_reduce_flg,
        "user_insurance_size": user_insurance_size,
        "user_free_insurance_size": user_free_insurance_size,
        "phone_from_user": phone_from_user,
        "cancel_apply_status": cancel_apply_status,
        "source": source if source is not None else "",
        "corp_count": corpInfo.get('corp_count', 0),
        "invalid_corp_count": corpInfo.get('invalid_corp_count', 0),
        "screenshot_flg": bool(screenshotInfo.get('screenshot', None)),
        "located_policy_flg": located_policy_flg,
        "located_policy_type": located_policy_type,
        "located_policy_size": located_policy_size
    }
